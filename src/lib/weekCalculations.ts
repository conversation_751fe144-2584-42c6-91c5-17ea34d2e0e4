export interface LifeData {
	birthDate: Date;
	weeksLived: number;
	weeksRemaining: number;
	totalWeeks: number;
	yearsLived: number;
	daysLived: number;
	percentageLived: number;
}

export function calculateLifeData(birthDate: Date): LifeData {
	const now = new Date();
	const timeDiff = now.getTime() - birthDate.getTime();
	const daysLived = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
	const weeksLived = Math.floor(daysLived / 7);
	
	// Assuming 90 years = 4680 weeks (90 * 52)
	const totalWeeks = 90 * 52;
	const weeksRemaining = Math.max(0, totalWeeks - weeksLived);
	const yearsLived = daysLived / 365.25;
	const percentageLived = (weeksLived / totalWeeks) * 100;

	return {
		birthDate,
		weeksLived,
		weeksRemaining,
		totalWeeks,
		yearsLived,
		daysLived,
		percentageLived
	};
}

export function formatDate(date: Date): string {
	return date.toLocaleDateString('en-US', {
		year: 'numeric',
		month: 'long',
		day: 'numeric'
	});
}

export function getLifeStage(yearsLived: number): string {
	if (yearsLived < 2) return 'infancy';
	if (yearsLived < 12) return 'childhood';
	if (yearsLived < 18) return 'adolescence';
	if (yearsLived < 30) return 'young adulthood';
	if (yearsLived < 50) return 'middle age';
	if (yearsLived < 70) return 'mature adulthood';
	return 'elderhood';
}
