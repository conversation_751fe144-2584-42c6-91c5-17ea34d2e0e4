@import 'tailwindcss';

/*
  The default border color has changed to `currentColor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
	*,
	::after,
	::before,
	::backdrop,
	::file-selector-button {
		border-color: var(--color-gray-200, currentColor);
	}
}

@theme {
	--breakpoint-sm: 25rem;
}

:root {
	--background: #ffffff;
	--foreground: #171717;
}

@media (prefers-color-scheme: dark) {
	:root {
		--background: #181818;
		--foreground: #ededed;
	}
}

html {
	scrollbar-gutter: stable both-edges;
}

body {
	color: var(--foreground);
	background: var(--background);
	background-color: #0a0a0a;
	min-height: 100vh;
	overflow-anchor: none;
	font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
	line-height: 1.6;
}
