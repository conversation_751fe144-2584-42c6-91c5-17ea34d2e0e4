<script lang="ts">
	import type { LifeData } from '../lib/weekCalculations';
	import { formatDate, getLifeStage } from '../lib/weekCalculations';

	export let lifeData: LifeData;

	$: lifeStage = getLifeStage(lifeData.yearsLived);
	$: weeksSinceNewYear = (() => {
		const now = new Date();
		const startOfYear = new Date(now.getFullYear(), 0, 1);
		const timeDiff = now.getTime() - startOfYear.getTime();
		return Math.floor(timeDiff / (1000 * 60 * 60 * 24 * 7));
	})();

	function formatNumber(num: number): string {
		return num.toLocaleString();
	}

	function getReflectiveMessage(): string {
		const percentage = lifeData.percentageLived;
		if (percentage < 25) {
			return "You're in the first quarter of life. Time feels infinite, and it largely is.";
		} else if (percentage < 50) {
			return "You've crossed the first quarter. Patterns are forming, choices are shaping your path.";
		} else if (percentage < 75) {
			return "Past the halfway point. Experience accumulates, wisdom deepens.";
		} else {
			return "In life's final quarter. Each moment carries the weight of all that came before.";
		}
	}
</script>

<div class="w-full max-w-4xl space-y-6">
	<!-- Simple stats section similar to original -->
	<div class="flex flex-col gap-2 text-center text-sm">
		<p class="text-orange-500">Each square represents one week of life</p>
		<p>Weeks lived: {formatNumber(lifeData.weeksLived)}</p>
		<p>Weeks remaining: {formatNumber(lifeData.weeksRemaining)}</p>
		<p class="text-neutral-400">
			{Math.round(lifeData.percentageLived)}% of your life has passed
		</p>
	</div>

	<!-- Expanded stats in cards -->
	<div class="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-3 sm:gap-4">
		<div class="text-center p-3 sm:p-4 rounded-lg border border-white/20 bg-[#141612]">
			<div class="text-lg sm:text-xl font-light text-white mb-1">
				{formatNumber(lifeData.weeksLived)}
			</div>
			<div class="text-xs text-neutral-400">weeks lived</div>
		</div>

		<div class="text-center p-3 sm:p-4 rounded-lg border border-white/20 bg-[#141612]">
			<div class="text-lg sm:text-xl font-light text-white mb-1">
				{formatNumber(lifeData.weeksRemaining)}
			</div>
			<div class="text-xs text-neutral-400">weeks remaining</div>
		</div>

		<div class="text-center p-3 sm:p-4 rounded-lg border border-white/20 bg-[#141612]">
			<div class="text-lg sm:text-xl font-light text-white mb-1">
				{Math.round(lifeData.yearsLived * 10) / 10}
			</div>
			<div class="text-xs text-neutral-400">years old</div>
		</div>

		<div class="text-center p-3 sm:p-4 rounded-lg border border-white/20 bg-[#141612]">
			<div class="text-lg sm:text-xl font-light text-white mb-1">
				{formatNumber(lifeData.daysLived)}
			</div>
			<div class="text-xs text-neutral-400">days lived</div>
		</div>

		<div class="text-center p-3 sm:p-4 rounded-lg border border-white/20 bg-[#141612]">
			<div class="text-lg sm:text-xl font-light text-white mb-1">
				{weeksSinceNewYear}
			</div>
			<div class="text-xs text-neutral-400">weeks this year</div>
		</div>

		<div class="text-center p-3 sm:p-4 rounded-lg border border-white/20 bg-[#141612]">
			<div class="text-sm sm:text-base font-light text-white mb-1 capitalize">
				{lifeStage}
			</div>
			<div class="text-xs text-neutral-400">life stage</div>
		</div>
	</div>

	<div class="text-center space-y-1 pt-2">
		<p class="text-xs text-neutral-500">
			Born on {formatDate(lifeData.birthDate)}
		</p>
		<p class="text-xs text-neutral-600 italic">
			{getReflectiveMessage()}
		</p>
	</div>
</div>
