<script lang="ts">
	import type { LifeData } from '../lib/weekCalculations';

	export let lifeData: LifeData;

	// 52 weeks per year, 90 years = 4680 weeks
	// Display as 52 columns (weeks per year) x 90 rows (years)
	const weeksPerYear = 52;
	const totalYears = 90;

	function getWeekStatus(weekIndex: number): 'lived' | 'current' | 'future' {
		if (weekIndex < lifeData.weeksLived - 1) return 'lived';
		if (weekIndex === lifeData.weeksLived - 1) return 'current';
		return 'future';
	}

	function getWeekClasses(weekIndex: number): string {
		const status = getWeekStatus(weekIndex);
		const baseClasses = 'w-1 h-1 sm:w-1.5 sm:h-1.5 md:w-2 md:h-2 transition-all duration-300 cursor-pointer rounded-full';

		switch (status) {
			case 'lived':
				return `${baseClasses} bg-red-500 hover:bg-red-400`;
			case 'current':
				return `${baseClasses} bg-green-500 animate-pulse`;
			case 'future':
				return `${baseClasses} bg-blue-500 hover:bg-blue-400`;
			default:
				return baseClasses;
		}
	}
</script>

<div class="w-full max-w-4xl">
	<div class="space-y-4">
		<div class="text-center space-y-2">
			<h2 class="text-xl font-light text-neutral-200">
				Each square represents one week of life
			</h2>
			<div class="flex flex-wrap items-center justify-center gap-4 sm:gap-6 text-xs sm:text-sm text-neutral-400">
				<div class="flex items-center gap-2">
					<div class="w-2 h-2 bg-red-500 rounded-full"></div>
					<span>Weeks lived</span>
				</div>
				<div class="flex items-center gap-2">
					<div class="w-2 h-2 bg-green-500 rounded-full"></div>
					<span>Current week</span>
				</div>
				<div class="flex items-center gap-2">
					<div class="w-2 h-2 bg-blue-500 rounded-full"></div>
					<span>Weeks remaining</span>
				</div>
			</div>
		</div>

		<div class="overflow-x-auto">
			<div class="grid grid-cols-52 gap-px min-w-max mx-auto" style="width: fit-content;">
				{#each Array(totalYears) as _, yearIndex}
					{#each Array(weeksPerYear) as _, weekInYear}
						{@const weekIndex = yearIndex * weeksPerYear + weekInYear}
						<div
							class={getWeekClasses(weekIndex)}
							title={`Year ${yearIndex + 1}, Week ${weekInYear + 1}`}
						></div>
					{/each}
				{/each}
			</div>
		</div>

		<div class="text-center">
			<p class="text-sm text-neutral-500">
				{lifeData.weeksLived.toLocaleString()} weeks lived • {lifeData.weeksRemaining.toLocaleString()} weeks remaining
			</p>
		</div>
	</div>
</div>
